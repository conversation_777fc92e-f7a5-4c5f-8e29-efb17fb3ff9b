package com.novitechie.rules;

import com.janetfilter.core.commons.DebugInfo;
import com.novitechie.LogUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ResourceRule {

    private static final List<String> PREVENT_LOAD_RESOURCE = new ArrayList<String>() {
        {
            add("/6c81ec87e55d331c267262e892427a3d93d76683.txt");
            add("/com/janetfilter");
        }
    };

    private static final List<String> IGNORE_RESOURCE = new ArrayList<String>() {
        {
            add("/com/janetfilter/core/utils/StringUtils.class");
        }
    };

    public static boolean check(String name) {
        boolean f = PREVENT_LOAD_RESOURCE.stream().anyMatch(name::startsWith);
        if (f) {
            boolean f1 = IGNORE_RESOURCE.stream().anyMatch(name::equals);
            if (!f1) {
                if (StackTraceRule.check()) {
                    DebugInfo.output("======================LoadResource: " + name);
                    LogUtil.printStackTrace();
                    return true;
                }
            }
        }
        return false;
    }
}
