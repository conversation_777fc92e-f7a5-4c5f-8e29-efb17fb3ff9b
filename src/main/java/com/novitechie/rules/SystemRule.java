package com.novitechie.rules;

import com.janetfilter.core.commons.DebugInfo;
import com.novitechie.LogUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SystemRule {

    private static final List<String> PREVENT_LOAD_ENV = new ArrayList<String>() {
        {
            add("IDEA_VM_OPTIONS");
            add("CLION_VM_OPTIONS");
            add("PHPSTORM_VM_OPTIONS");
            add("GOLAND_VM_OPTIONS");
            add("PYCHARM_VM_OPTIONS");
            add("WEBSTORM_VM_OPTIONS");
            add("WEBIDE_VM_OPTIONS");
            add("RIDER_VM_OPTIONS");
            add("DATAGRIP_VM_OPTIONS");
            add("RUBYMINE_VM_OPTIONS");
            add("APPCODE_VM_OPTIONS");
            add("DATASPELL_VM_OPTIONS");
            add("GATEWAY_VM_OPTIONS");
            add("JETBRAINS_CLIENT_VM_OPTIONS");
            add("JETBRAINSCLIENT_VM_OPTIONS");
            add("JANF_DEBUG");
            add("JANF_OUTPUT");
        }
    };

    private static final List<String> PREVENT_LOAD_PROPERTY = new ArrayList<String>() {
        {
            add("janf.debug");
            add("janf.output");
        }
    };

    public static boolean checkEnv(String name) {
        boolean f = PREVENT_LOAD_ENV.stream().anyMatch(name::equals);
        if (f) {
            if (StackTraceRule.check()) {
                DebugInfo.output("======================LoadEnv: " + name);
                LogUtil.printStackTrace();
                return true;
            }
        }
        return false;
    }

    public static boolean checkProperty(String name) {
        boolean f = PREVENT_LOAD_PROPERTY.stream().anyMatch(name::equals);
        if (f) {
            if (StackTraceRule.check()) {
                DebugInfo.output("======================LoadProperty: " + name);
                LogUtil.printStackTrace();
                return true;
            }
        }
        return false;
    }
}
