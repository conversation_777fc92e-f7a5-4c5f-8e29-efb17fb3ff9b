package com.novitechie.rules;

import com.janetfilter.core.commons.DebugInfo;
import com.novitechie.LogUtil;

import java.util.ArrayList;
import java.util.List;

public class LoadClassRule {

    private static final List<String> PREVENT_LOAD_PACKAGES = new ArrayList<String>() {
        {
            // add("com.novitechie");
            add("com.janetfilter");
            add("jdk.internal.org.objectweb.asm");
        }
    };

    private static final List<String> IGNORE_CLASS = new ArrayList<String>() {
        {
            add("com.janetfilter.core.utils.StringUtils");
        }
    };

    public static void check(String name) throws Exception {
        boolean f = PREVENT_LOAD_PACKAGES.stream().anyMatch(name::startsWith);
        if (f) {
            boolean f2 = IGNORE_CLASS.stream().anyMatch(name::equals);
            if (!f2) {
                DebugInfo.output("======================LoadClass: " + name);
                LogUtil.printStackTrace();
                throw new ClassNotFoundException(name);
            }
        }
    }

}
