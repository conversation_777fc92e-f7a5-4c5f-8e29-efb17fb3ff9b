package com.novitechie.rules;

import com.janetfilter.core.commons.DebugInfo;
import com.novitechie.LogUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class IdeaPluginRule {

    private static final List<String> PREVENT_LOAD_PLUGINS = new ArrayList<String>() {
        {
            add("io.zhile.research.ide-eval-resetter");
            add("Nasller.License");
            add("nasller");
            add("manifold.ij");
        }
    };

    public static boolean check(String name) {
        boolean f = PREVENT_LOAD_PLUGINS.stream().anyMatch(name::equals);
        if (f) {
            DebugInfo.output("======================LoadPlugin: " + name);
            LogUtil.printStackTrace();
        }
        return f;
    }
}
