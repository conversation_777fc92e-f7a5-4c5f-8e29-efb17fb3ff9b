package com.novitechie.rules;

import com.janetfilter.core.commons.DebugInfo;
import com.novitechie.LogUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ClassRule {


    private static final List<String> PREVENT_LOAD_METHOD = new ArrayList<String>() {
        {
            add("findBootstrapClass");
        }
    };

    public static void checkMethod(String name) throws Exception {
        if (PREVENT_LOAD_METHOD.stream().anyMatch(name::equals)) {
            DebugInfo.output("======================LoadMethod: " + name);
            LogUtil.printStackTrace();
            throw new NoSuchMethodException();
        }
    }
}
